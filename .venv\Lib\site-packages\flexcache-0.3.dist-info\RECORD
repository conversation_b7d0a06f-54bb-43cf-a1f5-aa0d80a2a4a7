flexcache-0.3.dist-info/AUTHORS,sha256=13_y1dVjVOeQrdtAm7Cva5ZbyYpl7C_JWNzKZR-5g5k,83
flexcache-0.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
flexcache-0.3.dist-info/LICENSE,sha256=onb6lE5r7P5RrjEwlMsEj3qmwuXMgjE46MHnT3P3nWE,1584
flexcache-0.3.dist-info/METADATA,sha256=EwgeArUr6xccpbJE_4tBLwPorIxuqtSfqDtVlVjea0U,6958
flexcache-0.3.dist-info/RECORD,,
flexcache-0.3.dist-info/WHEEL,sha256=oiQVh_5PnQM0E3gPdiz09WCNmwiHDMaGer_elqB3coM,92
flexcache-0.3.dist-info/top_level.txt,sha256=OO6PhB1AdDTf_UI5d_xUpAi6g_5qqmjnzXSzJ6EPpQg,10
flexcache/__init__.py,sha256=3dMkBJ_XINAReD3NQMTSAbBC9BpcSqyvBc69551Ci9o,1324
flexcache/__pycache__/__init__.cpython-310.pyc,,
flexcache/__pycache__/flexcache.cpython-310.pyc,,
flexcache/flexcache.py,sha256=lcZ7wwIH7kYO2cNfHx3exlEwo-UNxNPNIiOkxYHA6ZA,14597
flexcache/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
flexcache/testsuite/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
flexcache/testsuite/__pycache__/__init__.cpython-310.pyc,,
flexcache/testsuite/__pycache__/test_byhash.cpython-310.pyc,,
flexcache/testsuite/__pycache__/test_bymtime.cpython-310.pyc,,
flexcache/testsuite/__pycache__/test_custom_diskcache.cpython-310.pyc,,
flexcache/testsuite/__pycache__/test_header.cpython-310.pyc,,
flexcache/testsuite/test_byhash.py,sha256=eAIkLsg1HtLO33TUdUBSqPbc2vsHH05ll_nD4oEna8g,3093
flexcache/testsuite/test_bymtime.py,sha256=QLYb9lTyLaR-msh_CQxqvCHgjp93511mAu7ZVIZ-fbQ,3287
flexcache/testsuite/test_custom_diskcache.py,sha256=AyMro70ryePsHeiVm7p4libo71za7b4DykOi434Opes,2063
flexcache/testsuite/test_header.py,sha256=pnt4C9BkkSsPlO_vJiPiMXOQk2ixGbo0hT0UF6mrh7g,4858
